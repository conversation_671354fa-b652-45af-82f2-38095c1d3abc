package com.ruoyi.issue.rule.service.impl;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.coupon.domain.DigCouponCode;
import com.ruoyi.issue.coupon.mapper.DigCouponCodeMapper;
import com.ruoyi.issue.coupon.mapper.DigCouponMapper;
import com.ruoyi.issue.quartz.domain.NewJob;
import com.ruoyi.issue.quartz.service.JobService;
import com.ruoyi.issue.quartz.util.CronUtils;
import com.ruoyi.issue.rule.constant.AssetRuleConstant;
import com.ruoyi.issue.rule.domain.DigAssetRuleRel;
import com.ruoyi.issue.rule.domain.DigPrioritySnapshot;
import com.ruoyi.issue.rule.mapper.DigAssetRuleRelMapper;
import com.ruoyi.issue.rule.mapper.DigPrioritySnapshotMapper;
import com.ruoyi.issue.pay.domain.DigAssetOrder;
import com.ruoyi.issue.pay.mapper.DigAssetOrderMapper;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.identify.constant.IdentifyConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.rule.mapper.DigSaleRuleMapper;
import com.ruoyi.issue.rule.domain.DigSaleRule;
import com.ruoyi.issue.rule.service.DigSaleRuleService;

/**
 * 发售规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigSaleRuleServiceImpl extends ServiceImpl<DigSaleRuleMapper, DigSaleRule> implements DigSaleRuleService {
    private final DigSaleRuleMapper digSaleRuleMapper;
    private final DigAssetRuleRelMapper digAssetRuleRelMapper;
    private final DigCouponMapper digCouponMapper;
    private final DigPrioritySnapshotMapper digPrioritySnapshotMapper;

    private final DigCouponCodeMapper digCouponCodeMapper;

    private final RedisCache redisCache;

    private final JobService jobService;

    private final DigAssetOrderMapper digAssetOrderMapper;

    private final IdentifyService identifyService;

    /**
     * 查询发售规则
     *
     * @param ruleId 发售规则主键
     * @return 发售规则
     */
    @Override
    public DigSaleRule selectDigSaleRuleByRuleId(Long ruleId) {
        return digSaleRuleMapper.selectDigSaleRuleByRuleId(ruleId);
    }

    /**
     * 查询发售规则列表
     *
     * @param digSaleRule 发售规则
     * @return 发售规则
     */
    @Override
    public List<DigSaleRule> selectDigSaleRuleList(DigSaleRule digSaleRule) {
        return digSaleRuleMapper.selectDigSaleRuleList(digSaleRule);
    }

    /**
     * 新增发售规则
     *
     * @param digSaleRule 发售规则
     * @return 结果
     */
    @Override
    public int insertDigSaleRule(DigSaleRule digSaleRule) {
        return digSaleRuleMapper.insertDigSaleRule(digSaleRule);
    }

    /**
     * 修改发售规则
     *
     * @param digSaleRule 发售规则
     * @return 结果
     */
    @Override
    public int updateDigSaleRule(DigSaleRule digSaleRule) {
        return digSaleRuleMapper.updateDigSaleRule(digSaleRule);
    }

    /**
     * 批量删除发售规则
     *
     * @param ruleIds 需要删除的发售规则主键
     * @return 结果
     */
    @Override
    public int deleteDigSaleRuleByRuleIds(Long[] ruleIds) {
        return digSaleRuleMapper.deleteDigSaleRuleByRuleIds(ruleIds);
    }

    /**
     * 删除发售规则信息
     *
     * @param ruleId 发售规则主键
     * @return 结果
     */
    @Override
    public int deleteDigSaleRuleByRuleId(Long ruleId) {
        return digSaleRuleMapper.deleteDigSaleRuleByRuleId(ruleId);
    }

    @Override
    public List<DigSaleRule> selectDigSaleRuleListByAssetId(Long assetId) {
        List<DigAssetRuleRel> assetRuleRels = digAssetRuleRelMapper.selectList(new QueryWrapper<DigAssetRuleRel>().eq("asset_id", assetId));
        if (assetRuleRels != null && assetRuleRels.size() > 0) {
            List<Long> ids = assetRuleRels.stream().map(DigAssetRuleRel::getRuleId).collect(Collectors.toList());
            if (ids.size() > 0) {
                List<DigSaleRule> saleRules = digSaleRuleMapper.selectList(new QueryWrapper<DigSaleRule>().in("rule_id", ids));
                saleRules.forEach(saleRule -> saleRule.setCouponName(digCouponMapper.selectDigCouponByCouponId(saleRule.getCouponId()).getCouponName()));
                return saleRules;
            }
        }
        return null;
    }

    @Override
    public void insertDigSaleRuleRel(DigAssetRuleRel digAssetRuleRel) {
        digAssetRuleRelMapper.insert(digAssetRuleRel);
    }

    @Override
    public Long selectDigAssetIdByRuleId(Long ruleId) {
        DigAssetRuleRel assetRuleRel = digAssetRuleRelMapper.selectOne(new QueryWrapper<DigAssetRuleRel>().eq("rule_id", ruleId));
        return assetRuleRel.getAssetId();
    }

    @Override
    public AjaxResult isMatch(Long userId, Long assetId) {
        //判断有没有在开售时间和限时时间内
        DigDigitalAsset digDigitalAsset = redisCache.getCacheObject(CacheConstants.DIG_ASSET_DETAIL_KEY + assetId);
        List<DigSaleRule> limitedSaleRules = getRuleFromRedis(assetId, AssetRuleConstant.ASSET_RULE_LIMITED_SALE);
        LocalDateTime now = LocalDateTime.now();
        if (limitedSaleRules.isEmpty()) {
            if (now.isBefore(digDigitalAsset.getSaleStartTime())) {
                return AjaxResult.warn("该数字资产还未开售");
            }
        } else {
            if (!isTimeBetween(digDigitalAsset.getSaleStartTime(), limitedSaleRules.get(0).getEndTime(), now)) {
                return AjaxResult.warn("该数字资产未在销售时间");
            }
        }
        // 分时销售规则检查 - 返回匹配的规则
        List<DigSaleRule> timeshareSaleRules = getRuleFromRedis(assetId, AssetRuleConstant.ASSET_RULE_TIMESHARE_SALE);
        if (!timeshareSaleRules.isEmpty()) {
            Optional<DigSaleRule> matchedTimeshareRule = timeshareSaleRules.stream()
                    .filter(rule -> isTimeBetween(rule.getStartTime(), rule.getEndTime(), now))
                    .findFirst();

            if (!matchedTimeshareRule.isPresent()) {
                return AjaxResult.warn("该数字资产未在销售时间");
            }
            // 判断是否达到限购数量
            if (matchedTimeshareRule.get().getLimitQuantity() != 0  ) {
                Long inventoryCount = redisCache.getCacheObject(CacheConstants.INVENTORY_COUNT_KEY + assetId+ "_" + Constants.INVENTORY_SALE_TYPE_ISSUE);
                long saleCount = digDigitalAsset.getIssueQuantity() - inventoryCount;
                if (saleCount >= matchedTimeshareRule.get().getLimitQuantity()) {
                    return AjaxResult.warn("该数字资产已达到限购数量");
                }
            }
        }
        // 优先购规则检查
        List<DigSaleRule> prioritySaleRules = getRuleFromRedis(assetId, AssetRuleConstant.ASSET_RULE_PRIORITY_SALE);
        if (!prioritySaleRules.isEmpty()) {
            Optional<DigSaleRule> matchedPriorityRule = prioritySaleRules.stream()
                    .filter(rule -> isTimeBetween(rule.getStartTime(), rule.getEndTime(), now))
                    .findFirst();

            if (!matchedPriorityRule.isPresent()) {
                // 开放销售，没有规则限制
                return null;
            }
            // 判断是否达到限购数量
            if (matchedPriorityRule.get().getLimitQuantity() != 0  ) {
                Long inventoryCount = redisCache.getCacheObject(CacheConstants.INVENTORY_COUNT_KEY + assetId+ "_" + Constants.INVENTORY_SALE_TYPE_ISSUE);
                long saleCount = digDigitalAsset.getIssueQuantity() - inventoryCount;
                if (saleCount >= matchedPriorityRule.get().getLimitQuantity()) {
                    return AjaxResult.warn("该数字资产已达到限购数量");
                }
            }
            // 判断是否有对应的券

            String setKey = CacheConstants.DIG_ASSET_RULE_PRIORITY_KEY + matchedPriorityRule.get().getRuleId();
            boolean member = redisCache.isMember(setKey, userId);
            if (!member) {
                return AjaxResult.warn("无优先购资格");
            }
        }

        // 个人/企业限购数量检查
        if (digDigitalAsset.getIndividualLimit() != null || digDigitalAsset.getEnterpriseLimit() != null) {
            // 获取用户身份类型
            Identify identify = identifyService.getIdentifyByUserId(userId);
            if (identify != null) {
                Long limitQuantity = null;
                String userType = identify.getIdentifyType();

                if (IdentifyConstants.IDENTIFY_TYPE_PERSON.equals(userType) && digDigitalAsset.getIndividualLimit() != null) {
                    limitQuantity = digDigitalAsset.getIndividualLimit();
                } else if (IdentifyConstants.IDENTIFY_TYPE_ENTERPRISE.equals(userType) && digDigitalAsset.getEnterpriseLimit() != null) {
                    limitQuantity = digDigitalAsset.getEnterpriseLimit();
                }

                if (limitQuantity != null && limitQuantity > 0) {
                    // 查询用户已购买数量
                    Long purchasedCount = getUserPurchasedCount(userId, assetId);
                    if (purchasedCount >= limitQuantity) {
                        return AjaxResult.warn("已达到限购数量");
                    }
                }
            }
        }

        return null;
    }

    private boolean isTimeBetween(LocalDateTime start, LocalDateTime end, LocalDateTime now) {
        // 处理跨天时间段（如 23:00 到 01:00）
        if (start.isAfter(end)) {
            return !now.isBefore(start) || !now.isAfter(end);
        }
        // 非跨天时间段
        return !now.isBefore(start) && !now.isAfter(end);
    }

    /**
     * 获取用户已购买数量
     * @param userId 用户ID
     * @param assetId 资产ID
     * @return 已购买数量
     */
    private Long getUserPurchasedCount(Long userId, Long assetId) {
        // 可以先从Redis缓存中获取，如果没有则查询数据库
        String cacheKey = "user_purchased_count:" + userId + ":" + assetId;
        Long count = redisCache.getCacheObject(cacheKey);
        if (count == null) {
            // 查询数据库中用户已购买的数量
            count = digAssetOrderMapper.selectCount(
                Wrappers.<DigAssetOrder>lambdaQuery()
                    .eq(DigAssetOrder::getBuyerId, userId)
                    .eq(DigAssetOrder::getAssetId, assetId)
                    .eq(DigAssetOrder::getStatusCd, Constants.DIG_ORDER_STATE_PAID)
            );
            // 缓存结果，设置较短的过期时间
            redisCache.setCacheObject(cacheKey, count, 5, TimeUnit.MINUTES);
        }
        return count;
    }

    @Override
    public void setRuleToRedis(Long assetId) {
        List<DigSaleRule> saleRules = selectDigSaleRuleListByAssetId(assetId);
        if(saleRules != null){
            List<DigSaleRule> limitedSaleRules = new ArrayList<>();
            List<DigSaleRule> timeshareSaleRules = new ArrayList<>();
            List<DigSaleRule> prioritySaleRules = new ArrayList<>();
            for (DigSaleRule saleRule : saleRules) {
                switch (saleRule.getRuleType()) {
                    case AssetRuleConstant.ASSET_RULE_LIMITED_SALE:
                        limitedSaleRules.add(saleRule);
                        break;
                    case AssetRuleConstant.ASSET_RULE_TIMESHARE_SALE:
                        timeshareSaleRules.add(saleRule);
                        break;
                    case AssetRuleConstant.ASSET_RULE_PRIORITY_SALE:
                        prioritySaleRules.add(saleRule);
                        break;
                }
            }
            redisCache.setCacheObject(CacheConstants.DIG_ASSET_RULE_KEY + assetId + "_limited", limitedSaleRules, 7, TimeUnit.DAYS);
            redisCache.setCacheObject(CacheConstants.DIG_ASSET_RULE_KEY + assetId + "_timeshare", timeshareSaleRules, 7, TimeUnit.DAYS);
            redisCache.setCacheObject(CacheConstants.DIG_ASSET_RULE_KEY + assetId + "_priority", prioritySaleRules, 7, TimeUnit.DAYS);
        }
    }

    @Override
    public List<DigSaleRule> getRuleFromRedis(Long assetId, String ruleType) {
        String key = CacheConstants.DIG_ASSET_RULE_KEY + assetId;
        switch (ruleType) {
            case AssetRuleConstant.ASSET_RULE_LIMITED_SALE:
                key += "_limited";
                break;
            case AssetRuleConstant.ASSET_RULE_TIMESHARE_SALE:
                key += "_timeshare";
                break;
            case AssetRuleConstant.ASSET_RULE_PRIORITY_SALE:
                key += "_priority";
                break;
            default:
        }
        if (redisCache.hasKey(key)) {
            return redisCache.getCacheObject(key);
        } else {
            List<DigSaleRule> saleRules = new ArrayList<>();
            List<DigAssetRuleRel> assetRuleRels = digAssetRuleRelMapper.selectList(new QueryWrapper<DigAssetRuleRel>().eq("asset_id", assetId));
            if (assetRuleRels != null && assetRuleRels.size() > 0) {
                List<Long> ids = assetRuleRels.stream().map(DigAssetRuleRel::getRuleId).collect(Collectors.toList());
                if (ids.size() > 0) {
                    saleRules = digSaleRuleMapper.selectList(new QueryWrapper<DigSaleRule>().in("rule_id", ids).eq("rule_type", ruleType).eq("statusCd", Constants.GENERAL_STATE_ENABLE));
                    return saleRules;
                }
            }
            redisCache.setCacheObject(key, saleRules, 7, TimeUnit.DAYS);
            return saleRules;
        }
    }

    @Override
    public void snapshotPriority(Long ruleId) {
        DigSaleRule rule = baseMapper.selectById(ruleId);
        digPrioritySnapshotMapper.delete(Wrappers.<DigPrioritySnapshot>lambdaQuery()
                .eq(DigPrioritySnapshot::getRuleId, ruleId));
        List<DigCouponCode> digCouponCodes = digCouponCodeMapper.selectList(Wrappers.<DigCouponCode>lambdaQuery()
                .select(DigCouponCode::getCouponCodeId, DigCouponCode::getCouponCode, DigCouponCode::getUserId)
                .eq(DigCouponCode::getCouponId, rule.getCouponId())
                .isNotNull(DigCouponCode::getUserId)
                .eq(DigCouponCode::getIsUsed, Constants.GENERAL_STATE_ENABLE)
                .groupBy(DigCouponCode::getUserId));
        List<DigPrioritySnapshot> prioritySnapshots = new ArrayList<>();
        if (digCouponCodes != null && digCouponCodes.size() > 0){
            for (DigCouponCode digCouponCode : digCouponCodes) {
                DigPrioritySnapshot prioritySnapshot = new DigPrioritySnapshot();
//                prioritySnapshot.setAssetId(assetId);
                prioritySnapshot.setRuleId(ruleId);
                prioritySnapshot.setUserId(digCouponCode.getUserId());
                prioritySnapshot.setSnapshotTime(LocalDateTime.now());
                prioritySnapshots.add(prioritySnapshot);
            }
        }
        baseMapper.insertSnapshotsBatch(prioritySnapshots);
        //将prioritySnapshots中的userId取出来转为set,并保存到redis的缓存中
        Set<Long> userIds = prioritySnapshots.stream().map(DigPrioritySnapshot::getUserId).collect(Collectors.toSet());
        String setKey = CacheConstants.DIG_ASSET_RULE_PRIORITY_KEY + ruleId;
        redisCache.deleteObject(setKey);
        redisCache.setCacheSet(setKey, userIds);
        redisCache.expire(setKey, 7, TimeUnit.DAYS);
        List<Long> collect = digCouponCodes.stream().map(DigCouponCode::getCouponCodeId).collect(Collectors.toList());
        digCouponCodeMapper.update(null,Wrappers.<DigCouponCode>lambdaUpdate()
                .in(DigCouponCode::getCouponCodeId, collect)
                .set(DigCouponCode::getIsUsed, Constants.GENERAL_STATE_DISABLE));
    }

    @Override
    public void addRule(DigSaleRule digSaleRule) {
        digSaleRule.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        baseMapper.insert(digSaleRule);
        DigAssetRuleRel digAssetRuleRel = new DigAssetRuleRel();
        BeanUtil.copyProperties(digSaleRule, digAssetRuleRel);
        insertDigSaleRuleRel(digAssetRuleRel);
            try {
                NewJob job = new NewJob();
                job.setJobName(digSaleRule.getRuleName()+"_快照定时任务");
                job.setJobGroup("PRIORITY");
                job.setInvokeTarget("ryTask.ryPrioritySnapshot("+digSaleRule.getRuleId()+")");
                //将rule中的startTime时间转化为cron表达式
                job.setCronExpression(CronUtils.getCronExpression(digSaleRule.getStartTime(),1));
                job.setMisfirePolicy("2");
                job.setConcurrent("1");
                job.setStatus("0");
                job.setCreateBy("system");
                jobService.insertJob(job);
                job.setStatus("0");
                jobService.resumeJob(job);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
    }


}
